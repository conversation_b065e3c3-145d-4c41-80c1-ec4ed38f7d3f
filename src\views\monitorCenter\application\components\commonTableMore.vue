<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-08-22 16:27:23
 * @LastEditors: wjb
 * @LastEditTime: 2025-08-22 16:28:25
-->
<template>
  <div>
    <el-table :data="datalist">
      <template v-for="(item, i) in keyLabelList">
        <el-table-column
          :key="i"
          :prop="item.key"
          :label="item.label"
          align="center"
          :min-width="item.width"
          :show-overflow-tooltip="item.showTooltip"
        >
          <template slot-scope="scope">
            {{ scope.row[item.key] }}
          </template>
        </el-table-column>
      </template>
      <el-table-column
        label="操作"
        align="center"
        width="180"
        v-if="showControl"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetail(scope.$index, scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    datalist: {
      type: Array,
      default: () => [],
    },
    keyLabelList: {
      type: Array,
      default: () => [],
    },
    showControl: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleDetail() {},
    itemClick(item) {
      this.$emit("itemClick", item);
    },
  },
};
</script>

<style lang="scss" scoped>
.color_red {
  color: red;
}
</style>
